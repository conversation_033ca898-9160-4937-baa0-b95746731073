<div class="inline-drawer">
    <div class="inline-drawer-toggle inline-drawer-header">
        <b>小白X</b>
        <div class="inline-drawer-icon fa-solid fa-circle-chevron-down down"></div>
    </div>
    <div class="inline-drawer-content">
        <div class="settings-menu">
            <div class="menu-tab active" data-target="js-memory">小白X</div>
            <div class="menu-tab" data-target="task">循环任务</div>
            <div class="menu-tab" data-target="instructions">说明指南</div>
        </div>

        <div class="js-memory settings-section" style="display: block;">
            <div class="flex-container">
                <input type="checkbox" id="xiaobaix_enabled" />
                <label for="xiaobaix_enabled">启用小白X</label>
            </div>
            <div class="flex-container">
                <input type="checkbox" id="xiaobaix_script_assistant" />
                <label for="xiaobaix_script_assistant">启用写卡助手</label>
                <i class="fa-solid fa-circle-info" title="勾选后，AI将获取小白X功能和ST脚本语言知识，帮助您创作角色卡"></i>
            </div>
            <div class="flex-container">
                <input type="checkbox" id="xiaobaix_preview_enabled" />
                <label for="xiaobaix_preview_enabled">启用消息预览</label>
                <i class="fa-solid fa-circle-info" title="在聊天框下方显示咖啡杯图标，点击可预览将发送给AI的完整消息"></i>
            </div>
            <div class="flex-container">
                <input type="checkbox" id="xiaobaix_sandbox" />
                <label for="xiaobaix_sandbox">沙盒模式</label>
            </div>
            <hr class="sysHR">

            <div class="flex-container">
                <input type="checkbox" id="xiaobaix_memory_enabled" />
                <label for="xiaobaix_memory_enabled">启用数据统计</label>
            </div>

            <div class="flex-container">
                <input type="checkbox" id="xiaobaix_memory_inject" />
                <label for="xiaobaix_memory_inject">注入统计信息到提示词</label>
            </div>

            <div class="flex-container">
                <label for="xiaobaix_memory_depth">信息注入深度:</label>
                <input type="number" id="xiaobaix_memory_depth" min="1" max="20" class="dark-number-input" />
            </div>

            <hr class="sysHR">

            <div style="font-size:0.9em; opacity:0.8; margin-top:10px;">
                <p><b>功能说明：</b></p>
                <p>1. 渲染被<code>```</code>包裹的任意形式的代码块内容为交互式界面</p>
                <p>2. 提供<code>STscript(command)</code>函数执行酒馆命令</p>
                <p>3. 历史数据统计：点击消息操作菜单右侧大脑图标查看统计</p>
                <p>4. 循环任务：自动执行设定好的斜杠命令</p>
                <p>5. 消息预览：查看AI接收到的完整消息内容</p>
            </div>

        </div>

        <div class="task settings-section" style="display: none;">
            <div class="flex-container">
                <input type="checkbox" id="scheduled_tasks_enabled" />
                <label for="scheduled_tasks_enabled">启用循环任务</label>
            </div>

            <div class="flex-container">
                <div id="add_global_task" class="menu_button menu_button_icon" title="添加全局任务">
                    <i class="fa-solid fa-plus"></i>
                    <small>+全局</small>
                </div>
                <div id="add_character_task" class="menu_button menu_button_icon" title="添加角色任务">
                    <i class="fa-solid fa-user-plus"></i>
                    <small>+ 角色</small>
                </div>
                <div id="test_all_tasks" class="menu_button menu_button_icon" title="测试所有任务">
                    <i class="fa-solid fa-play"></i>
                    <small>测试全部</small>
                </div>
            </div>
            <div id="global_tasks_block" class="padding5">
                <div>
                    <strong>全局任务</strong>
                </div>
                <small>这些任务在所有角色中的聊天都会执行</small>
                <div id="global_tasks_list" class="flex-container task-container flexFlowColumn"></div>
            </div>

            <hr />

            <div id="character_tasks_block" class="padding5">
                <div class="flex-container alignItemsCenter">
                    <strong>角色任务</strong>
                    <div class="flex-container">
                        <div id="export_character_tasks" class="menu_button menu_button_icon" title="导出角色任务">
                            <i class="fa-solid fa-download"></i>
                            <small>导出</small>
                        </div>
                        <div id="import_character_tasks" class="menu_button menu_button_icon" title="导入角色任务">
                            <i class="fa-solid fa-upload"></i>
                            <small>导入</small>
                        </div>
                    </div>
                </div>
                <small>这些任务只在当前角色的聊天中执行</small>
                <div id="character_tasks_list" class="flex-container task-container flexFlowColumn"></div>

                <input type="file" id="import_tasks_file" accept=".json" style="display: none;" />
            </div>

            <hr class="sysHR">

            <div style="font-size:0.9em; opacity:0.8; margin-top:10px;">
                <p><b>功能说明：</b></p>
                <p>1. 输入<code>/xbqte {{任务名称}} </code>可以手动激活任务</p>
                <p>2. 输入<code>/xbset {{任务名称}} {{间隔楼层}} </code>可以调整循环任务中, 激活的楼层间隔</p>
                <p>3. 导出/入角色卡时, 角色任务会随角色卡一起导出/入(需要安装本插件)</p>
            </div>
        </div>

        <div class="instructions settings-section" style="display: none;">
            <div style="font-size:0.9em; opacity:0.8; margin-top:10px;">
                <p><b>使用示例：</b></p>
                <pre
                    style="background: #2a2a2a; padding: 15px; border-radius: 6px; border-left: 4px solid #007acc; white-space: pre-wrap; word-wrap: break-word;">
// 显示提示消息
await STscript('/echo 你好世界！');

// 创建变量
await STscript('/setvar key=天气 晴朗');
const weather = await STscript('/getvar 天气');

// 动态更新界面
document.querySelector('#status').textContent = `当前天气：${weather}`;

// 创建按钮
const button = document.createElement('button');
button.textContent = '点击我';
button.onclick = () => STscript('/xbqte task');
document.body.appendChild(button);
</pre>

            </div>
        </div>
    </div>
</div>
</div>

<div id="task_editor_template" style="display: none;">
    <div class="task_editor">
        <h3>任务编辑器</h3>
        <div class="flex-container flexFlowColumn">
            <div class="flex1">
                <label for="task_name_edit">任务名称</label>
                <input class="task_name_edit text_pole textarea_compact" type="text" placeholder="输入任务名称" />
            </div>
            <div class="flex1">
                <label for="task_commands_edit">斜杠命令</label>
                <textarea class="task_commands_edit text_pole wide100p textarea_compact" placeholder="输入要执行的斜杠命令, eg: /buttons labels=[&quot;男&quot;,&quot;女&quot;] 你是？ | /echo 你选择了：{{pipe}}"></textarea>
            </div>
            <div class="flex-container">
                <div class="flex1">
                    <label for="task_interval_edit">楼层间隔</label>
                    <input class="task_interval_edit text_pole textarea_compact" type="number" min="0" max="100" />
                    <small>每隔多少楼层执行一次（设为0手动激活）</small>
                </div>
                <div class="flex1">
                    <label for="task_floor_type_edit">楼层类型</label>
                    <select class="task_floor_type_edit text_pole textarea_compact">
                        <option value="all">全部楼层</option>
                        <option value="user">用户楼层</option>
                        <option value="llm">LLM楼层</option>
                    </select>
                    <small>选择计算楼层的方式</small>
                </div>
            </div>
            <div class="flex-container">
                <div class="flex1">
                    <label for="task_type_edit">任务类型</label>
                    <select class="task_type_edit text_pole textarea_compact">
                        <option value="global">全局任务</option>
                        <option value="character">角色任务</option>
                    </select>
                </div>
                <div class="flex1">
                    <label for="task_trigger_timing_edit">触发时机</label>
                    <select class="task_trigger_timing_edit text_pole textarea_compact">
                        <option value="after_ai">AI消息后</option>
                        <option value="before_user">用户消息后</option>
                        <option value="per_turn">每轮对话</option>
                    </select>
                    <small>选择任务执行的时机</small>
                </div>
            </div>
            <div class="flex1">
                <label class="checkbox flex-container">
                    <input type="checkbox" class="task_enabled_edit" />
                    <span>启用任务</span>
                </label>
            </div>
        </div>
    </div>
</div>

<div id="task_item_template" style="display: none;">
    <div class="task-item flex-container flexnowrap">
        <span class="drag-handle menu-handle">&#9776;</span>
        <div class="task_name flexGrow overflow-hidden"></div>
        <div class="flex-container flexnowrap">
            <label class="checkbox flex-container">
                <input type="checkbox" class="disable_task" />
                <span class="task-toggle-on fa-solid fa-toggle-on" title="禁用任务"></span>
                <span class="task-toggle-off fa-solid fa-toggle-off" title="启用任务"></span>
            </label>
            <div class="test_task menu_button" title="测试任务"><i class="fa-solid fa-play"></i></div>
            <div class="edit_task menu_button" title="编辑任务"><i class="fa-solid fa-pencil"></i></div>
            <div class="delete_task menu_button" title="删除任务"><i class="fa-solid fa-trash"></i></div>
        </div>
    </div>
</div>

<div id="task_preview_template" style="display: none;">
    <div class="task-preview">
        <strong class="task-preview-name"></strong> <span class="task-preview-interval"></span>
        <div class="task-commands task-preview-commands"></div>
    </div>
</div>

<style>
    .settings-menu {
        display: flex;
        margin-bottom: 10px;
        border-bottom: 1px solid #444;
    }

    .menu-tab {
        flex: 1;
        padding: 2px 8px;
        text-align: center;
        cursor: pointer;
        color: #ccc;
        border: none;
        transition: color 0.2s ease;
        font-weight: 500;
    }

    .menu-tab:hover {
        color: #fff;
    }

    .menu-tab.active {
        color: #007acc;
        border-bottom: 2px solid #007acc;
    }

    .settings-section {
        padding: 10px 0;
    }
</style>
